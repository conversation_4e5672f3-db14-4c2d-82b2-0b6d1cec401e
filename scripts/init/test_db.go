package main

import (
	"fmt"
	"log"
	"os"

	"gopkg.in/yaml.v2"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"loop/internal/config"
)

func main() {
	fmt.Println("Testing database connection...")
	
	// 尝试不同的配置文件路径
	configPaths := []string{
		"configs/config.yaml",       // 从项目根目录运行
		"../configs/config.yaml",    // 从scripts目录运行
		"../../configs/config.yaml", // 从scripts/init目录运行
	}

	var configFile string
	for _, path := range configPaths {
		if _, err := os.Stat(path); err == nil {
			configFile = path
			break
		}
	}

	if configFile == "" {
		log.Fatalf("config file not found in any of the expected paths")
	}

	fmt.Printf("Found config file: %s\n", configFile)

	cfg := &config.Config{}
	yamlFile, err := os.ReadFile(configFile)
	if err != nil {
		log.Fatalf("failed to read config file %s: %v", configFile, err)
	}

	if err = yaml.Unmarshal(yamlFile, cfg); err != nil {
		log.Fatalf("failed to unmarshal config: %v", err)
	}

	fmt.Printf("Database URL: %s\n", cfg.DbConfig.Url)

	// 连接数据库
	db, err := gorm.Open(mysql.Open(cfg.DbConfig.Url), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	fmt.Println("Database connection successful!")

	// 测试查询
	var result struct {
		Version string `gorm:"column:version()"`
	}
	if err := db.Raw("SELECT version()").Scan(&result).Error; err != nil {
		log.Fatalf("Failed to query database: %v", err)
	}

	fmt.Printf("MySQL version: %s\n", result.Version)
}
