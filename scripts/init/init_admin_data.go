package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"golang.org/x/crypto/bcrypt"
	"gopkg.in/yaml.v2"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"loop/internal/config"
	"loop/internal/constants"
	"loop/internal/model"
)

// loadConfig 加载配置文件，自动检测路径
func loadConfig() (*config.Config, error) {
	// 尝试不同的配置文件路径
	configPaths := []string{
		"configs/config.yaml",    // 从项目根目录运行
		"../configs/config.yaml", // 从scripts目录运行
	}

	var configFile string
	for _, path := range configPaths {
		if _, err := os.Stat(path); err == nil {
			configFile = path
			break
		}
	}

	if configFile == "" {
		return nil, fmt.Errorf("config file not found in any of the expected paths")
	}

	cfg := &config.Config{}
	yamlFile, err := os.ReadFile(configFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file %s: %v", configFile, err)
	}

	if err = yaml.Unmarshal(yamlFile, cfg); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %v", err)
	}

	fmt.Printf("配置文件加载成功: %s\n", configFile)
	return cfg, nil
}

func main() {
	fmt.Println("开始初始化管理员数据...")

	// 加载配置
	cfg, err := loadConfig()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接数据库
	fmt.Println("正在连接数据库...")
	db, err := connectDB(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	fmt.Println("数据库连接成功")

	// 初始化数据
	if err := initAdminData(db); err != nil {
		log.Fatalf("Failed to initialize admin data: %v", err)
	}

	fmt.Println("Admin data initialization completed successfully!")

	// 验证数据
	fmt.Println("\n=== 开始数据验证 ===")
	if err := verifyData(db); err != nil {
		log.Fatalf("Data verification failed: %v", err)
	}
	fmt.Println("=== 数据验证完成 ===")
}

func connectDB(cfg *config.Config) (*gorm.DB, error) {
	// 直接使用配置中的DSN URL
	db, err := gorm.Open(mysql.Open(cfg.DbConfig.Url), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, err
	}

	// 自动迁移表结构
	if err := db.AutoMigrate(model.Models...); err != nil {
		return nil, fmt.Errorf("failed to auto migrate tables: %v", err)
	}

	return db, nil
}

func initAdminData(db *gorm.DB) error {
	// 初始化系统角色
	if err := initSysRoles(db); err != nil {
		return fmt.Errorf("failed to init sys roles: %v", err)
	}

	// 初始化系统用户
	if err := initSysUsers(db); err != nil {
		return fmt.Errorf("failed to init sys users: %v", err)
	}

	// 初始化分类
	if err := initCategories(db); err != nil {
		return fmt.Errorf("failed to init categories: %v", err)
	}

	// 初始化VIP等级
	if err := initVIPs(db); err != nil {
		return fmt.Errorf("failed to init VIPs: %v", err)
	}

	// 初始化商品
	if err := initTradeProducts(db); err != nil {
		return fmt.Errorf("failed to init trade products: %v", err)
	}

	// 初始化权益组
	if err := initBenefitGroups(db); err != nil {
		return fmt.Errorf("failed to init benefit groups: %v", err)
	}

	// 初始化权益
	if err := initBenefits(db); err != nil {
		return fmt.Errorf("failed to init benefits: %v", err)
	}

	// 初始化VIP权益关联
	if err := initVipBenefits(db); err != nil {
		return fmt.Errorf("failed to init vip benefits: %v", err)
	}

	return nil
}

func initSysRoles(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM sys_roles").Error; err != nil {
		return err
	}

	roles := []model.SysRole{
		{
			ModelAutoId: model.ModelAutoId{Id: 1, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:        "admin",
			RoleLevel:   2,
		},
		{
			ModelAutoId: model.ModelAutoId{Id: 2, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:        "normal",
			RoleLevel:   0,
		},
	}

	for _, role := range roles {
		if err := db.Create(&role).Error; err != nil {
			return err
		}
	}

	return nil
}

func initSysUsers(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM sys_users").Error; err != nil {
		return err
	}
	bytes, _ := bcrypt.GenerateFromPassword([]byte("111111"), constants.PassWordCost)
	users := []model.SysUser{
		{
			ModelAutoId:    model.ModelAutoId{Id: 1, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			UserName:       "admin",
			PasswordDigest: string(bytes), // secret
			RoleLevel:      2,
			Status:         0,
		},
	}

	for _, user := range users {
		if err := db.Create(&user).Error; err != nil {
			return err
		}
	}

	return nil
}

func initCategories(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM categories").Error; err != nil {
		return err
	}

	categories := []model.Category{
		// 等级分类 (CategoryTypeId: "1")
		{
			Model:          model.Model{Id: "1000000000000000001", CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:           "零基础",
			Description:    "适合完全没有英语基础的学习者",
			Priority:       1,
			CategoryTypeId: "1",
		},
		{
			Model:          model.Model{Id: "1000000000000000002", CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:           "中级",
			Description:    "适合有一定英语基础的学习者",
			Priority:       2,
			CategoryTypeId: "1",
		},
		{
			Model:          model.Model{Id: "1000000000000000003", CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:           "高级",
			Description:    "适合英语基础较好的学习者",
			Priority:       3,
			CategoryTypeId: "1",
		},

		// 场景分类 (CategoryTypeId: "2")
		{
			Model:          model.Model{Id: "1000000000000000004", CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:           "演讲",
			Description:    "公开演讲和演示技巧",
			Priority:       1,
			CategoryTypeId: "2",
		},
		{
			Model:          model.Model{Id: "1000000000000000005", CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:           "考试",
			Description:    "各类英语考试准备",
			Priority:       2,
			CategoryTypeId: "2",
		},
		{
			Model:          model.Model{Id: "1000000000000000006", CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:           "面试",
			Description:    "求职面试英语技巧",
			Priority:       3,
			CategoryTypeId: "2",
		},
		{
			Model:          model.Model{Id: "1000000000000000007", CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:           "出国",
			Description:    "出国留学和生活英语",
			Priority:       4,
			CategoryTypeId: "2",
		},
		{
			Model:          model.Model{Id: "1000000000000000008", CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:           "写作",
			Description:    "英语写作技巧和练习",
			Priority:       5,
			CategoryTypeId: "2",
		},

		// 主题分类 (CategoryTypeId: "3")
		{
			Model:          model.Model{Id: "1000000000000000009", CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:           "TED",
			Description:    "TED演讲学习资源",
			Priority:       1,
			CategoryTypeId: "3",
		},
		{
			Model:          model.Model{Id: "1000000000000000010", CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:           "YouTube",
			Description:    "YouTube视频学习资源",
			Priority:       2,
			CategoryTypeId: "3",
		},
		{
			Model:          model.Model{Id: "1000000000000000011", CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:           "博客",
			Description:    "英语博客和文章",
			Priority:       3,
			CategoryTypeId: "3",
		},
		{
			Model:          model.Model{Id: "1000000000000000012", CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:           "影视",
			Description:    "电影和电视剧学习资源",
			Priority:       4,
			CategoryTypeId: "3",
		},
		{
			Model:          model.Model{Id: "1000000000000000013", CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:           "书籍",
			Description:    "英语书籍和文学作品",
			Priority:       5,
			CategoryTypeId: "3",
		},
	}

	for _, category := range categories {
		if err := db.Create(&category).Error; err != nil {
			return err
		}
	}

	return nil
}

func initVIPs(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM vips").Error; err != nil {
		return err
	}

	vips := []model.VIP{
		{
			ModelAutoId: model.ModelAutoId{Id: 1, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:        "普通会员",
			Level:       1,
		},
		{
			ModelAutoId: model.ModelAutoId{Id: 2, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:        "PRO会员",
			Level:       100,
		},
		{
			ModelAutoId: model.ModelAutoId{Id: 3, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:        "ULTRA会员",
			Level:       1000,
		},
	}

	for _, vip := range vips {
		if err := db.Create(&vip).Error; err != nil {
			return err
		}
	}

	return nil
}

func initTradeProducts(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM trade_products").Error; err != nil {
		return err
	}

	products := []model.TradeProduct{
		{
			ModelAutoId:    model.ModelAutoId{Id: 1, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:           "PRO会员1个月",
			Type:           1,
			IsSubscription: 1,
			Price:          29.00,
			OriginPrice:    29.00,
			IosProductId:   "com.lsenglish.pro.month",
			Currency:       "USD",
			Terminal:       1, // Android
			Days:           30,
			VipID:          2,
		},
		{
			ModelAutoId:    model.ModelAutoId{Id: 2, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:           "PRO会员3个月",
			Type:           1,
			IsSubscription: 1,
			Price:          69.00,
			OriginPrice:    69.00,
			IosProductId:   "com.lsenglish.pro.quarter",
			Currency:       "USD",
			Terminal:       1, // Android
			Days:           90,
			VipID:          2,
		},
		{
			ModelAutoId:    model.ModelAutoId{Id: 3, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:           "ULTRA会员1个月",
			Type:           1,
			IsSubscription: 1,
			Price:          49.00,
			OriginPrice:    49.00,
			IosProductId:   "com.lsenglish.ultra.month",
			Currency:       "USD",
			Terminal:       1, // Android
			Days:           30,
			VipID:          3,
		},
		{
			ModelAutoId:    model.ModelAutoId{Id: 4, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:           "PRO会员1个月",
			Type:           1,
			IsSubscription: 1,
			Price:          29.00,
			OriginPrice:    29.00,
			IosProductId:   "com.lsenglish.pro.month",
			Currency:       "USD",
			Terminal:       2, // iOS
			Days:           30,
			VipID:          2,
		},
		{
			ModelAutoId:    model.ModelAutoId{Id: 5, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:           "PRO会员3个月",
			Type:           1,
			IsSubscription: 1,
			Price:          69.00,
			OriginPrice:    69.00,
			IosProductId:   "com.lsenglish.pro.quarter",
			Currency:       "USD",
			Terminal:       2, // iOS
			Days:           90,
			VipID:          2,
		},
		{
			ModelAutoId:    model.ModelAutoId{Id: 6, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:           "ULTRA会员1个月",
			Type:           1,
			IsSubscription: 1,
			Price:          49.00,
			OriginPrice:    49.00,
			IosProductId:   "com.lsenglish.ultra.month",
			Currency:       "USD",
			Terminal:       2, // iOS
			Days:           30,
			VipID:          3,
		},
	}

	for _, product := range products {
		if err := db.Create(&product).Error; err != nil {
			return err
		}
	}

	return nil
}

func initBenefitGroups(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM benefit_groups").Error; err != nil {
		return err
	}

	groups := []model.BenefitGroup{
		{
			ModelAutoId: model.ModelAutoId{Id: 1, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:        "视频大小限制组",
			Code:        "VIDEO_SIZE_LIMIT",
			Status:      1,
		},
		{
			ModelAutoId: model.ModelAutoId{Id: 2, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:        "AI调用次数组",
			Code:        "AI_CALLS_LIMIT",
			Status:      1,
		},
		{
			ModelAutoId: model.ModelAutoId{Id: 3, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:        "字幕对话次数组",
			Code:        "SUBTITLE_DIALOGUE_LIMIT",
			Status:      1,
		},
	}

	for _, group := range groups {
		if err := db.Create(&group).Error; err != nil {
			return err
		}
	}

	return nil
}

func initBenefits(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM benefits").Error; err != nil {
		return err
	}

	benefits := []model.Benefit{
		// 普通会员权益
		{
			ModelAutoId:      model.ModelAutoId{Id: 1, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:             "普通会员视频限制",
			Code:             "UPLOAD_LIMIT_BASIC",
			Level:            10,
			CycleType:        6, // 无周期
			CycleCount:       1,
			BenefitCount:     50, // 50MB
			Sort:             1,
			Status:           1,
			BenefitGroupID:   1,
			BenefitGroupName: "视频大小限制组",
			BenefitGroupCode: "VIDEO_SIZE_LIMIT",
			Description:      "普通会员单视频最大50MB",
		},
		{
			ModelAutoId:      model.ModelAutoId{Id: 2, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:             "普通会员AI调用次数",
			Code:             "AI_CALLS_BASIC",
			Level:            10,
			CycleType:        1, // 日周期
			CycleCount:       1,
			BenefitCount:     10, // 每日10次
			Sort:             1,
			Status:           1,
			BenefitGroupID:   2,
			BenefitGroupName: "AI调用次数组",
			BenefitGroupCode: "AI_CALLS_LIMIT",
			Description:      "普通会员每日10次AI调用",
		},
		{
			ModelAutoId:      model.ModelAutoId{Id: 3, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:             "普通会员字幕对话次数",
			Code:             "SUBTITLE_DIALOGUE_BASIC",
			Level:            10,
			CycleType:        1, // 日周期
			CycleCount:       1,
			BenefitCount:     5, // 每日5次
			Sort:             1,
			Status:           1,
			BenefitGroupID:   3,
			BenefitGroupName: "字幕对话次数组",
			BenefitGroupCode: "SUBTITLE_DIALOGUE_LIMIT",
			Description:      "普通会员每日5次字幕对话",
		},

		// PRO会员权益
		{
			ModelAutoId:      model.ModelAutoId{Id: 4, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:             "Pro会员视频限制",
			Code:             "UPLOAD_LIMIT_PRO",
			Level:            20,
			CycleType:        6, // 无周期
			CycleCount:       1,
			BenefitCount:     200, // 200MB
			Sort:             2,
			Status:           1,
			BenefitGroupID:   1,
			BenefitGroupName: "视频大小限制组",
			BenefitGroupCode: "VIDEO_SIZE_LIMIT",
			Description:      "Pro会员单视频最大200MB",
		},
		{
			ModelAutoId:      model.ModelAutoId{Id: 5, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:             "Pro会员AI调用次数",
			Code:             "AI_CALLS_PRO",
			Level:            20,
			CycleType:        1, // 日周期
			CycleCount:       1,
			BenefitCount:     100, // 每日100次
			Sort:             2,
			Status:           1,
			BenefitGroupID:   2,
			BenefitGroupName: "AI调用次数组",
			BenefitGroupCode: "AI_CALLS_LIMIT",
			Description:      "Pro会员每日100次AI调用",
		},
		{
			ModelAutoId:      model.ModelAutoId{Id: 6, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:             "Pro会员字幕对话次数",
			Code:             "SUBTITLE_DIALOGUE_PRO",
			Level:            20,
			CycleType:        1, // 日周期
			CycleCount:       1,
			BenefitCount:     50, // 每日50次
			Sort:             2,
			Status:           1,
			BenefitGroupID:   3,
			BenefitGroupName: "字幕对话次数组",
			BenefitGroupCode: "SUBTITLE_DIALOGUE_LIMIT",
			Description:      "Pro会员每日50次字幕对话",
		},

		// ULTRA会员权益
		{
			ModelAutoId:      model.ModelAutoId{Id: 7, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:             "Ultra会员视频限制",
			Code:             "UPLOAD_LIMIT_ULTRA",
			Level:            30,
			CycleType:        6, // 无周期
			CycleCount:       1000,
			BenefitCount:     5120, // 5GB
			Sort:             3,
			Status:           1,
			BenefitGroupID:   1,
			BenefitGroupName: "视频大小限制组",
			BenefitGroupCode: "VIDEO_SIZE_LIMIT",
			Description:      "Ultra会员单视频最大5GB",
		},
		{
			ModelAutoId:      model.ModelAutoId{Id: 8, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:             "Ultra会员AI调用次数",
			Code:             "AI_CALLS_ULTRA",
			Level:            30,
			CycleType:        3, // 月周期
			CycleCount:       1000,
			BenefitCount:     1000, // 每月1000次
			Sort:             3,
			Status:           1,
			BenefitGroupID:   2,
			BenefitGroupName: "AI调用次数组",
			BenefitGroupCode: "AI_CALLS_LIMIT",
			Description:      "Ultra会员每月1000次AI调用",
		},
		{
			ModelAutoId:      model.ModelAutoId{Id: 9, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Name:             "Ultra会员字幕对话次数",
			Code:             "SUBTITLE_DIALOGUE_ULTRA",
			Level:            30,
			CycleType:        3, // 月周期
			CycleCount:       1000,
			BenefitCount:     1000, // 每月1000次
			Sort:             3,
			Status:           1,
			BenefitGroupID:   3,
			BenefitGroupName: "字幕对话次数组",
			BenefitGroupCode: "SUBTITLE_DIALOGUE_LIMIT",
			Description:      "Ultra会员每月1000次字幕对话",
		},
	}

	for _, benefit := range benefits {
		if err := db.Create(&benefit).Error; err != nil {
			return err
		}
	}

	return nil
}

func initVipBenefits(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM vip_benefits").Error; err != nil {
		return err
	}

	vipBenefits := []model.VipBenefit{
		// 普通会员权益关联
		{
			ModelAutoId:     model.ModelAutoId{Id: 1, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			VipID:           1,
			VipLevel:        1,
			BenefitGroupID:  1,
			BenefitID:       1,
			BenefitCode:     "UPLOAD_LIMIT_BASIC",
			CreateTime:      time.Now(),
			CreateTimestamp: time.Now().Unix(),
		},
		{
			ModelAutoId:     model.ModelAutoId{Id: 2, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			VipID:           1,
			VipLevel:        1,
			BenefitGroupID:  2,
			BenefitID:       2,
			BenefitCode:     "AI_CALLS_BASIC",
			CreateTime:      time.Now(),
			CreateTimestamp: time.Now().Unix(),
		},
		{
			ModelAutoId:     model.ModelAutoId{Id: 3, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			VipID:           1,
			VipLevel:        1,
			BenefitGroupID:  3,
			BenefitID:       3,
			BenefitCode:     "SUBTITLE_DIALOGUE_BASIC",
			CreateTime:      time.Now(),
			CreateTimestamp: time.Now().Unix(),
		},

		// PRO会员权益关联
		{
			ModelAutoId:     model.ModelAutoId{Id: 4, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			VipID:           2,
			VipLevel:        100,
			BenefitGroupID:  1,
			BenefitID:       4,
			BenefitCode:     "UPLOAD_LIMIT_PRO",
			CreateTime:      time.Now(),
			CreateTimestamp: time.Now().Unix(),
		},
		{
			ModelAutoId:     model.ModelAutoId{Id: 5, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			VipID:           2,
			VipLevel:        100,
			BenefitGroupID:  2,
			BenefitID:       5,
			BenefitCode:     "AI_CALLS_PRO",
			CreateTime:      time.Now(),
			CreateTimestamp: time.Now().Unix(),
		},
		{
			ModelAutoId:     model.ModelAutoId{Id: 6, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			VipID:           2,
			VipLevel:        100,
			BenefitGroupID:  3,
			BenefitID:       6,
			BenefitCode:     "SUBTITLE_DIALOGUE_PRO",
			CreateTime:      time.Now(),
			CreateTimestamp: time.Now().Unix(),
		},

		// ULTRA会员权益关联
		{
			ModelAutoId:     model.ModelAutoId{Id: 7, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			VipID:           3,
			VipLevel:        1000,
			BenefitGroupID:  1,
			BenefitID:       7,
			BenefitCode:     "UPLOAD_LIMIT_ULTRA",
			CreateTime:      time.Now(),
			CreateTimestamp: time.Now().Unix(),
		},
		{
			ModelAutoId:     model.ModelAutoId{Id: 8, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			VipID:           3,
			VipLevel:        1000,
			BenefitGroupID:  2,
			BenefitID:       8,
			BenefitCode:     "AI_CALLS_ULTRA",
			CreateTime:      time.Now(),
			CreateTimestamp: time.Now().Unix(),
		},
		{
			ModelAutoId:     model.ModelAutoId{Id: 9, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			VipID:           3,
			VipLevel:        1000,
			BenefitGroupID:  3,
			BenefitID:       9,
			BenefitCode:     "SUBTITLE_DIALOGUE_ULTRA",
			CreateTime:      time.Now(),
			CreateTimestamp: time.Now().Unix(),
		},
	}

	for _, vipBenefit := range vipBenefits {
		if err := db.Create(&vipBenefit).Error; err != nil {
			return err
		}
	}

	return nil
}

func verifyData(db *gorm.DB) error {
	// 验证系统角色
	var roleCount int64
	if err := db.Model(&model.SysRole{}).Count(&roleCount).Error; err != nil {
		return fmt.Errorf("failed to count sys roles: %v", err)
	}
	fmt.Printf("✓ 系统角色数量: %d\n", roleCount)

	// 验证系统用户
	var userCount int64
	if err := db.Model(&model.SysUser{}).Count(&userCount).Error; err != nil {
		return fmt.Errorf("failed to count sys users: %v", err)
	}
	fmt.Printf("✓ 系统用户数量: %d\n", userCount)

	// 验证分类
	var categoryCount int64
	if err := db.Model(&model.Category{}).Count(&categoryCount).Error; err != nil {
		return fmt.Errorf("failed to count categories: %v", err)
	}
	fmt.Printf("✓ 分类数量: %d\n", categoryCount)

	// 验证VIP等级
	var vipCount int64
	if err := db.Model(&model.VIP{}).Count(&vipCount).Error; err != nil {
		return fmt.Errorf("failed to count vips: %v", err)
	}
	fmt.Printf("✓ VIP等级数量: %d\n", vipCount)

	// 验证商品
	var productCount int64
	if err := db.Model(&model.TradeProduct{}).Count(&productCount).Error; err != nil {
		return fmt.Errorf("failed to count trade products: %v", err)
	}
	fmt.Printf("✓ 商品数量: %d\n", productCount)

	// 验证权益组
	var benefitGroupCount int64
	if err := db.Model(&model.BenefitGroup{}).Count(&benefitGroupCount).Error; err != nil {
		return fmt.Errorf("failed to count benefit groups: %v", err)
	}
	fmt.Printf("✓ 权益组数量: %d\n", benefitGroupCount)

	// 验证权益
	var benefitCount int64
	if err := db.Model(&model.Benefit{}).Count(&benefitCount).Error; err != nil {
		return fmt.Errorf("failed to count benefits: %v", err)
	}
	fmt.Printf("✓ 权益数量: %d\n", benefitCount)

	// 验证VIP权益关联
	var vipBenefitCount int64
	if err := db.Model(&model.VipBenefit{}).Count(&vipBenefitCount).Error; err != nil {
		return fmt.Errorf("failed to count vip benefits: %v", err)
	}
	fmt.Printf("✓ VIP权益关联数量: %d\n", vipBenefitCount)

	// 验证管理员用户是否可以登录
	var admin model.SysUser
	if err := db.Where("username = ?", "admin").First(&admin).Error; err != nil {
		return fmt.Errorf("admin user not found: %v", err)
	}
	fmt.Printf("✓ 管理员用户验证成功: %s (角色等级: %d)\n", admin.UserName, admin.RoleLevel)

	fmt.Println("所有数据验证通过！")
	return nil
}
