package main

import (
	"fmt"
	"log"
	"os"

	"gopkg.in/yaml.v2"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"loop/internal/config"
)

func main() {
	fmt.Println("Checking database tables...")
	
	// 加载配置
	configPaths := []string{
		"configs/config.yaml",
		"../configs/config.yaml",
		"../../configs/config.yaml",
	}

	var configFile string
	for _, path := range configPaths {
		if _, err := os.Stat(path); err == nil {
			configFile = path
			break
		}
	}

	if configFile == "" {
		log.Fatalf("config file not found")
	}

	cfg := &config.Config{}
	yamlFile, err := os.ReadFile(configFile)
	if err != nil {
		log.Fatalf("failed to read config file: %v", err)
	}

	if err = yaml.Unmarshal(yamlFile, cfg); err != nil {
		log.Fatalf("failed to unmarshal config: %v", err)
	}

	// 连接数据库
	db, err := gorm.Open(mysql.Open(cfg.DbConfig.Url), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 检查表是否存在
	tables := []string{
		"sys_roles",
		"sys_users", 
		"categories",
		"vips",
		"trade_products",
		"benefit_groups",
		"benefits",
		"vip_benefits",
	}

	for _, table := range tables {
		if db.Migrator().HasTable(table) {
			fmt.Printf("✓ Table %s exists\n", table)
		} else {
			fmt.Printf("✗ Table %s does NOT exist\n", table)
		}
	}
}
