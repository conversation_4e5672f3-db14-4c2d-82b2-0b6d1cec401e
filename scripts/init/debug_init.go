package main

import (
	"fmt"
	"log"
	"os"

	"gopkg.in/yaml.v2"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"loop/internal/config"
	"loop/internal/model"
)

func main() {
	fmt.Println("Debug: Starting...")

	// 加载配置
	configPaths := []string{
		"configs/config.yaml",
		"../configs/config.yaml",
		"../../configs/config.yaml",
	}

	var configFile string
	for _, path := range configPaths {
		if _, err := os.Stat(path); err == nil {
			configFile = path
			break
		}
	}

	if configFile == "" {
		log.Fatalf("config file not found")
	}

	fmt.Printf("Debug: Found config file: %s\n", configFile)

	cfg := &config.Config{}
	yamlFile, err := os.ReadFile(configFile)
	if err != nil {
		log.Fatalf("failed to read config file: %v", err)
	}

	if err = yaml.Unmarshal(yamlFile, cfg); err != nil {
		log.Fatalf("failed to unmarshal config: %v", err)
	}

	fmt.Println("Debug: Config loaded successfully")

	// 连接数据库
	db, err := gorm.Open(mysql.Open(cfg.DbConfig.Url), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	fmt.Println("Debug: Database connected successfully")

	// 测试简单的数据库操作
	var count int64
	if err := db.Model(&model.SysRole{}).Count(&count).Error; err != nil {
		log.Fatalf("Failed to count sys_roles: %v", err)
	}

	fmt.Printf("Debug: Current sys_roles count: %d\n", count)

	// 测试创建一个简单的记录
	testRole := model.SysRole{
		Name:      "test_role",
		RoleLevel: 1,
	}

	if err := db.Create(&testRole).Error; err != nil {
		fmt.Printf("Debug: Failed to create test role: %v\n", err)
	} else {
		fmt.Printf("Debug: Test role created successfully with ID: %d\n", testRole.Id)

		// 删除测试记录
		db.Delete(&testRole)
		fmt.Println("Debug: Test role deleted")
	}

	fmt.Println("Debug: All tests completed successfully!")
}
