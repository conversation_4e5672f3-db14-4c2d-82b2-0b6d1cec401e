package main

import (
	"fmt"
	"log"
	"os"

	"gopkg.in/yaml.v2"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"loop/internal/config"
	"loop/internal/model"
)

func main() {
	fmt.Println("=== 开始最小化初始化脚本 ===")
	
	// 加载配置
	configPaths := []string{
		"configs/config.yaml",
		"../configs/config.yaml", 
		"../../configs/config.yaml",
	}

	var configFile string
	for _, path := range configPaths {
		if _, err := os.Stat(path); err == nil {
			configFile = path
			break
		}
	}

	if configFile == "" {
		log.Fatalf("config file not found")
	}

	fmt.Printf("找到配置文件: %s\n", configFile)

	cfg := &config.Config{}
	yamlFile, err := os.ReadFile(configFile)
	if err != nil {
		log.Fatalf("failed to read config file: %v", err)
	}

	if err = yaml.Unmarshal(yamlFile, cfg); err != nil {
		log.Fatalf("failed to unmarshal config: %v", err)
	}

	fmt.Println("配置加载成功")

	// 连接数据库
	db, err := gorm.Open(mysql.Open(cfg.DbConfig.Url), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	fmt.Println("数据库连接成功")

	// 删除现有数据并创建一个简单的角色
	if err := db.Exec("DELETE FROM sys_roles").Error; err != nil {
		log.Fatalf("Failed to delete existing roles: %v", err)
	}

	fmt.Println("已删除现有角色数据")

	// 创建新角色
	role := model.SysRole{
		Name:      "admin",
		RoleLevel: 2,
	}

	if err := db.Create(&role).Error; err != nil {
		log.Fatalf("Failed to create role: %v", err)
	}

	fmt.Printf("成功创建角色，ID: %d\n", role.Id)

	// 验证数据
	var count int64
	if err := db.Model(&model.SysRole{}).Count(&count).Error; err != nil {
		log.Fatalf("Failed to count roles: %v", err)
	}

	fmt.Printf("当前角色总数: %d\n", count)
	fmt.Println("=== 最小化初始化脚本完成 ===")
}
